export class Footer {
  constructor() {
    this.element = null;
  }

  render() {
    this.element = document.createElement('footer');
    this.element.id = 'footer';
    this.element.innerHTML = `
      <div class="footer-wrapper">
        <!-- Large background text -->
        <div class="footer-background-text">AUREAVOICE</div>

        <div class="container">

          <!-- Empty div to keep footer content centered -->
          <div class="footer-spacer"></div>

          <div class="footer-content">
            <div class="footer-section footer-brand">
              <h3 class="footer-brand-title">AUREAVOICE</h3>
              <p class="footer-description">AureaVoice Menggunakan Teknologi Artificial Intelligence Untuk Latihan Vokal dan Peningkatan Kemampuan Bernyanyi</p>
            </div>

            <div class="footer-section">
              <h3 class="footer-title">Internal</h3>
              <ul class="footer-links">
                <li><a href="#/">Home</a></li>
                <li><a href="#/dashboard">Dashboard</a></li>
                <li><a href="#/latihan"><PERSON><PERSON><PERSON></a></li>
                <li><a href="#/tentang">Tentang</a></li>
                <li><a href="#/faq">FAQ</a></li>
              </ul>
            </div>

            <div class="footer-section">
              <h3 class="footer-title">Komunitas</h3>
              <ul class="footer-links">
                <li><a href="#">Telegram Grup</a></li>
                <li><a href="#">Blog</a></li>
                <li><a href="#">Event</a></li>
                <li><a href="#">Kontak</a></li>
              </ul>
            </div>

            <div class="footer-section">
              <h3 class="footer-title">Resources</h3>
              <ul class="footer-links">
                <li><a href="#">Get Involved</a></li>
                <li><a href="#">Press Releases</a></li>
                <li><a href="#">Privacy Policy</a></li>
                <li><a href="#">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          

          <div class="footer-bottom">
            <div class="copyright">
              <p>© 2025 AureaVoice All rights reserved.</p>
            </div>
          </div>
        </div>
      </div>
    `;

    return this.element;
  }

  mount(container) {
    if (!this.element) {
      this.render();
    }
    container.appendChild(this.element);
  }

  unmount() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
}
