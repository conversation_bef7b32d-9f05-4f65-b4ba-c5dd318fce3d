/* Footer Styles */
#footer {
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
  margin-top: 4rem;
  position: relative;
  overflow: hidden;
  height: 1500px;
  display: flex;
  flex-direction: column;
}

.footer-wrapper {
  position: relative;
  padding: 5rem 0 0 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.footer-background-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 13rem;
  font-weight: 900;
  color: rgba(0, 0, 0, 0.03);
  z-index: 1;
  pointer-events: none;
  user-select: none;
  white-space: nowrap;
  word-spacing: 0.5em;
}

.container {
  max-width: none;
  width: 90%;
  margin: 0 auto;
  padding: 0 3rem;
  position: relative;
  z-index: 2;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 5rem;
  margin-bottom: auto;
  flex-grow: 1;
  align-content: start;
  padding: 2rem 0;
  width: 100%;
  max-width: 1500px;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-brand {
  max-width: 400px;
}

.footer-brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 2rem;
}

.footer-description {
  color: #64748b;
  font-size: 1.25rem;
  line-height: 1.7;
  margin-bottom: 0;
}

.footer-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2.5rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 1.25rem;
}

.footer-links a {
  color: #64748b;
  text-decoration: none;
  font-size: 1.125rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #2563eb;
}

.footer-spacer {
  flex-grow: 1;
}

.footer-bottom {
  border-top: 1px solid #e2e8f0;
  padding: 3rem 0;
  margin-top: auto;
  margin-bottom: 50px;
}

.copyright {
  text-align: left;
}

.copyright p {
  color: #94a3b8;
  font-size: 1.125rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }

  .footer-brand {
    grid-column: 1 / -1;
    max-width: none;
    margin-bottom: 2rem;
  }

  .footer-background-text {
    font-size: 9rem;
    word-spacing: 0.4em;
  }

  .container {
    padding: 0 2rem;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .footer-wrapper {
    padding: 4rem 0 0 0;
  }

  .container {
    padding: 0 1.5rem;
  }

  .footer-background-text {
    font-size: 7rem;
    word-spacing: 0.3em;
  }

  .footer-bottom {
    margin-bottom: 30px;
  }
}

@media (max-width: 480px) {
  .footer-background-text {
    font-size: 5rem;
    word-spacing: 0.2em;
  }

  .footer-brand-title {
    font-size: 2rem;
  }

  .footer-description {
    font-size: 1rem;
  }

  .footer-title {
    font-size: 1.25rem;
  }

  .footer-links a {
    font-size: 1rem;
  }

  .footer-bottom {
    margin-bottom: 20px;
  }

  .copyright p {
    font-size: 1rem;
  }
}
